import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type Book from '$lib/domain/Book';
import type BookReadStatusType from '$lib/domain/BookReadStatusType';

export default class BookReadStatusesApiClient extends MainApiClient {
    public async show(id: BookReadStatusType): Promise<ShowResponse> {
        return await this.get(`/book-read-statuses/${id}`);
    }

    public async setBookStatus(bookId: string, statusType: BookReadStatusType): Promise<SetBookStatusResponse> {
        return await this.post(`/book-read-statuses/add/${bookId}`, {statusType});
    }

    public async removeBookStatus(bookId: string): Promise<RemoveBookStatusResponse> {
        return await this.post(`/book-read-statuses/remove/${bookId}`);
    }
}

interface ShowResponse extends BaseApiResponse {
    data: Book[];
}

interface SetBookStatusResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface RemoveBookStatusResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}
