<?php declare(strict_types=1);

namespace App\Services;

use App\Enums\BookReadStatusType;
use App\Models\BookReadStatus;
use Illuminate\Support\Facades\Auth;

final class BookStatusService
{
    public function getBooksGroupedByStatus(bool $isLimited = true): array
    {
        $userBookReadStatuses = Auth::user()->bookReadStatuses;

        $groupedBooks = [];
        $bookReadStatusTypes = BookReadStatusType::cases();

        foreach ($bookReadStatusTypes as $bookReadStatusType) {
            $groupedBooks[$bookReadStatusType->value] = $userBookReadStatuses->filter(
                fn (BookReadStatus $bookReadStatus) => $bookReadStatus->type == $bookReadStatusType
            );

            if ($isLimited) {
                $groupedBooks[$bookReadStatusType->value] = $groupedBooks[$bookReadStatusType->value]->take(5);
            }

            $groupedBooks[$bookReadStatusType->value] = $groupedBooks[$bookReadStatusType->value]->map(
                fn (BookReadStatus $bookReadStatus) => $bookReadStatus->book->toDTO()
            )->values();
        }

        return $groupedBooks;
    }
}
