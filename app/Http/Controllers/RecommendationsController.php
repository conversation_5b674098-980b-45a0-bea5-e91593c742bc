<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Services\RecommendationsService;
use Illuminate\Support\Facades\Auth;

final class RecommendationsController extends Controller
{
    private RecommendationsService $recommendationsService;

    public function __construct()
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ]);
        $this->recommendationsService = new RecommendationsService();
    }

    public function index(): array
    {
        $user = Auth::user();

        return [
            'currentlyReading' => $user->currentlyReadingBookStatuses->take(4)->map->book->map->toDto(),
            'forYou' => $user->recommendations->map->book->map->toDto(),
            'becauseYouLiked' => $this->recommendationsService->getBecauseYouLikedRecommendations(),
        ];
    }
}
